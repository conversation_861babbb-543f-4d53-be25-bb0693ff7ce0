import OnboardingOverlay, { OnboardingStep } from '@/components/OnboardingOverlay';
import { markOnboardingCompleted, ONBOARDING_PAGES } from '@/utils/storage';
import React, { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';

interface RecipeEditOnboardingProps {
  visible: boolean;
  onComplete: () => void;
  onSkip: () => void;
  recipeNameRef?: React.RefObject<View | null>;
  ingredientsRef?: React.RefObject<View | null>;
  difficultyRef?: React.RefObject<View | null>;
  stepsRef?: React.RefObject<View | null>;
}

const RecipeEditOnboarding: React.FC<RecipeEditOnboardingProps> = ({
  visible,
  onComplete,
  onSkip,
  recipeNameRef,
  ingredientsRef,
  difficultyRef,
  stepsRef,
}) => {
  const { t } = useTranslation();
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [elementPositions, setElementPositions] = useState<{
    recipeName?: { x: number; y: number; width: number; height: number };
    ingredients?: { x: number; y: number; width: number; height: number };
    difficulty?: { x: number; y: number; width: number; height: number };
    steps?: { x: number; y: number; width: number; height: number };
  }>({});

  const measureElement = useCallback((
    ref: React.RefObject<View | null>,
    key: keyof typeof elementPositions
  ) => {
    if (ref?.current) {
      ref.current.measureInWindow((x, y, width, height) => {
        setElementPositions(prev => ({
          ...prev,
          [key]: { x, y, width, height }
        }));
      });
    }
  }, []);

  const measureAllElements = useCallback(() => {
    if (recipeNameRef) measureElement(recipeNameRef, 'recipeName');
    if (ingredientsRef) measureElement(ingredientsRef, 'ingredients');
    if (difficultyRef) measureElement(difficultyRef, 'difficulty');
    if (stepsRef) measureElement(stepsRef, 'steps');
  }, [recipeNameRef, ingredientsRef, difficultyRef, stepsRef, measureElement]);

  React.useEffect(() => {
    if (visible) {
      // Delay measurement to ensure UI is rendered
      const timer = setTimeout(() => {
        measureAllElements();
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [visible, measureAllElements]);

  const steps: OnboardingStep[] = [
    {
      id: 'welcome',
      title: t('onboarding.recipeEdit.welcome.title'),
      description: t('onboarding.recipeEdit.welcome.description'),
      position: 'center',
      showSkip: true,
    },
    {
      id: 'recipe_name',
      title: t('onboarding.recipeEdit.recipeName.title'),
      description: t('onboarding.recipeEdit.recipeName.description'),
      targetElement: elementPositions.recipeName,
      position: 'bottom',
    },
    {
      id: 'ingredients',
      title: t('onboarding.recipeEdit.ingredients.title'),
      description: t('onboarding.recipeEdit.ingredients.description'),
      targetElement: elementPositions.ingredients,
      position: 'bottom',
    },
    {
      id: 'difficulty',
      title: t('onboarding.recipeEdit.difficulty.title'),
      description: t('onboarding.recipeEdit.difficulty.description'),
      targetElement: elementPositions.difficulty,
      position: 'bottom',
    },
    {
      id: 'steps',
      title: t('onboarding.recipeEdit.steps.title'),
      description: t('onboarding.recipeEdit.steps.description'),
      targetElement: elementPositions.steps,
      position: 'top',
    },
    {
      id: 'complete',
      title: t('onboarding.recipeEdit.complete.title'),
      description: t('onboarding.recipeEdit.complete.description'),
      position: 'center',
      showSkip: false,
    },
  ];

  const handleNext = useCallback(() => {
    if (currentStepIndex < steps.length - 1) {
      setCurrentStepIndex(currentStepIndex + 1);
    }
  }, [currentStepIndex, steps.length]);

  const handlePrevious = useCallback(() => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(currentStepIndex - 1);
    }
  }, [currentStepIndex]);

  const handleComplete = useCallback(async () => {
    try {
      await markOnboardingCompleted(ONBOARDING_PAGES.RECIPE_EDIT);
      setCurrentStepIndex(0);
      onComplete();
    } catch (error) {
      console.error('Error marking onboarding as completed:', error);
      onComplete();
    }
  }, [onComplete]);

  const handleSkip = useCallback(async () => {
    try {
      await markOnboardingCompleted(ONBOARDING_PAGES.RECIPE_EDIT);
      setCurrentStepIndex(0);
      onSkip();
    } catch (error) {
      console.error('Error marking onboarding as completed:', error);
      onSkip();
    }
  }, [onSkip]);

  return (
    <OnboardingOverlay
      visible={visible}
      steps={steps}
      currentStepIndex={currentStepIndex}
      onNext={handleNext}
      onPrevious={handlePrevious}
      onSkip={handleSkip}
      onComplete={handleComplete}
    />
  );
};

export default RecipeEditOnboarding;
