import { Alert, Linking, Platform } from 'react-native';

// App Store URLs for the app
const APP_STORE_URLS = {
  ios: 'https://apps.apple.com/app/id1234567890', // Replace with actual App Store URL
  android: 'https://play.google.com/store/apps/details?id=com.odupangjjeong.mychef',
};

/**
 * Show a native App Store review prompt dialog
 * For now, this will show a custom dialog since expo-store-review is not installed
 * @param onReviewRequested Callback when user chooses to review
 * @param onDismissed Callback when user dismisses the dialog
 */
export const showReviewPrompt = (
  onReviewRequested?: () => void,
  onDismissed?: () => void
): void => {
  Alert.alert(
    'Enjoying the app?',
    'If you love using our recipe generator, would you mind taking a moment to rate it? It really helps us improve!',
    [
      {
        text: 'Not Now',
        style: 'cancel',
        onPress: () => {
          console.log('User dismissed review prompt');
          onDismissed?.();
        },
      },
      {
        text: 'Rate App',
        style: 'default',
        onPress: () => {
          console.log('User chose to rate the app');
          openAppStore();
          onReviewRequested?.();
        },
      },
    ],
    { cancelable: true, onDismiss: onDismissed }
  );
};

/**
 * Open the app store page for the app
 */
export const openAppStore = async (): Promise<void> => {
  try {
    const url = Platform.OS === 'ios' ? APP_STORE_URLS.ios : APP_STORE_URLS.android;
    
    const canOpen = await Linking.canOpenURL(url);
    if (canOpen) {
      await Linking.openURL(url);
    } else {
      console.error('Cannot open app store URL:', url);
      Alert.alert(
        'Error',
        'Unable to open the app store. Please search for our app manually.'
      );
    }
  } catch (error) {
    console.error('Error opening app store:', error);
    Alert.alert(
      'Error',
      'Unable to open the app store. Please try again later.'
    );
  }
};

/**
 * Check if the device supports native review prompts
 * For now, this always returns false since expo-store-review is not installed
 * @returns Boolean indicating if native review prompts are supported
 */
export const isReviewPromptSupported = (): boolean => {
  // TODO: Return true when expo-store-review is installed and configured
  return false;
};

/**
 * Show a native review prompt if supported, otherwise show custom dialog
 * @param onReviewRequested Callback when user chooses to review
 * @param onDismissed Callback when user dismisses the dialog
 */
export const requestReview = (
  onReviewRequested?: () => void,
  onDismissed?: () => void
): void => {
  if (isReviewPromptSupported()) {
    // TODO: Use expo-store-review when available
    // StoreReview.requestReview();
    console.log('Native review prompt would be shown here');
    onReviewRequested?.();
  } else {
    showReviewPrompt(onReviewRequested, onDismissed);
  }
};
