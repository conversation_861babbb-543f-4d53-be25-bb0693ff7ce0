import { Platform } from 'react-native';
import mobileAds, { AdEventType, BannerAdSize, RewardedAd, RewardedAdEventType, TestIds } from 'react-native-google-mobile-ads';

// Ad unit IDs for production
const AD_UNIT_IDS = {
  ios: {
    banner: 'ca-app-pub-2947221843793912/1234567890', // Replace with actual iOS banner ad unit ID
    rewarded: 'ca-app-pub-2947221843793912/1234567891', // Replace with actual iOS rewarded ad unit ID
  },
  android: {
    banner: 'ca-app-pub-2947221843793912/1234567890', // Replace with actual Android banner ad unit ID
    rewarded: 'ca-app-pub-2947221843793912/1234567891', // Replace with actual Android rewarded ad unit ID
  },
};

// Use test ads in development
const getBannerAdUnitId = (): string => {
  if (__DEV__) {
    return TestIds.BANNER;
  }

  return Platform.OS === 'ios' ? AD_UNIT_IDS.ios.banner : AD_UNIT_IDS.android.banner;
};

const getRewardedAdUnitId = (): string => {
  if (__DEV__) {
    return TestIds.REWARDED;
  }

  return Platform.OS === 'ios' ? AD_UNIT_IDS.ios.rewarded : AD_UNIT_IDS.android.rewarded;
};

/**
 * Initialize Google Mobile Ads
 * @returns Promise that resolves when ads are initialized
 */
export const initializeAds = async (): Promise<void> => {
  try {
    await mobileAds().initialize();
    console.log('Google Mobile Ads initialized successfully');
  } catch (error) {
    console.error('Failed to initialize Google Mobile Ads:', error);
    throw error;
  }
};

/**
 * Get the banner ad unit ID for the current platform
 * @returns Banner ad unit ID
 */
export const getBannerAdId = (): string => {
  return getBannerAdUnitId();
};

/**
 * Get the banner ad size
 * @returns Banner ad size configuration
 */
export const getBannerAdSize = () => {
  return BannerAdSize.BANNER; // 320x50
};

/**
 * Check if ads are available (not in a restricted region, etc.)
 * @returns Promise that resolves to boolean indicating if ads are available
 */
export const areAdsAvailable = async (): Promise<boolean> => {
  try {
    // You can add additional checks here if needed
    // For now, we'll assume ads are always available unless in development without test ads
    return true;
  } catch (error) {
    console.error('Error checking ad availability:', error);
    return false;
  }
};

// Video Ad functionality
let rewardedAd: RewardedAd | null = null;

/**
 * Create and load a rewarded video ad
 * @returns Promise that resolves when the ad is loaded
 */
export const createRewardedAd = async (): Promise<void> => {
  try {
    rewardedAd = RewardedAd.createForAdRequest(getRewardedAdUnitId(), {
      requestNonPersonalizedAdsOnly: true,
    });

    return new Promise((resolve, reject) => {
      if (!rewardedAd) {
        reject(new Error('Failed to create rewarded ad'));
        return;
      }

      const unsubscribeLoaded = rewardedAd.addAdEventListener(RewardedAdEventType.LOADED, () => {
        console.log('Rewarded ad loaded successfully');
        unsubscribeLoaded();
        resolve();
      });

      const unsubscribeError = rewardedAd.addAdEventListener(AdEventType.ERROR, (error) => {
        console.error('Rewarded ad failed to load:', error);
        unsubscribeLoaded();
        unsubscribeError();
        reject(error);
      });

      rewardedAd.load();
    });
  } catch (error) {
    console.error('Error creating rewarded ad:', error);
    throw error;
  }
};

/**
 * Show the rewarded video ad
 * @returns Promise that resolves when the ad is shown and user earns reward
 */
export const showRewardedAd = async (): Promise<boolean> => {
  try {
    if (!rewardedAd) {
      throw new Error('Rewarded ad not loaded. Call createRewardedAd() first.');
    }

    return new Promise((resolve, reject) => {
      if (!rewardedAd) {
        reject(new Error('Rewarded ad not available'));
        return;
      }

      let adShown = false;
      let rewardEarned = false;

      const unsubscribeEarnedReward = rewardedAd.addAdEventListener(RewardedAdEventType.EARNED_REWARD, (reward) => {
        console.log('User earned reward:', reward);
        rewardEarned = true;
      });

      const unsubscribeClosed = rewardedAd.addAdEventListener(AdEventType.CLOSED, () => {
        console.log('Rewarded ad closed');
        unsubscribeEarnedReward();
        unsubscribeClosed();
        unsubscribeError();

        // Clean up the ad instance
        rewardedAd = null;

        resolve(rewardEarned);
      });

      const unsubscribeError = rewardedAd.addAdEventListener(AdEventType.ERROR, (error) => {
        console.error('Rewarded ad error:', error);
        unsubscribeEarnedReward();
        unsubscribeClosed();
        unsubscribeError();

        // Clean up the ad instance
        rewardedAd = null;

        reject(error);
      });

      rewardedAd.show();
      adShown = true;
    });
  } catch (error) {
    console.error('Error showing rewarded ad:', error);
    throw error;
  }
};

/**
 * Check if a rewarded ad is loaded and ready to show
 * @returns Boolean indicating if ad is ready
 */
export const isRewardedAdLoaded = (): boolean => {
  return rewardedAd !== null;
};
