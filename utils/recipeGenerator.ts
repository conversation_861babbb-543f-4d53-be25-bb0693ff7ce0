import { DifficultyLevel, Recipe } from './storage';

// Mock AI API endpoint - replace with actual AI service
const AI_API_ENDPOINT = 'https://api.openai.com/v1/chat/completions';
const AI_API_KEY = process.env.EXPO_PUBLIC_OPENAI_API_KEY || 'your-api-key-here';

interface RecipeGenerationRequest {
  ingredients: string[];
  difficulty: DifficultyLevel;
  language?: string;
}

interface AIResponse {
  title: string;
  ingredients: string[];
  steps: string[];
  difficulty: DifficultyLevel;
}

/**
 * Generate a recipe using AI based on ingredients and difficulty level
 * @param request Recipe generation parameters
 * @returns Promise that resolves to a generated recipe
 */
export const generateRecipe = async (request: RecipeGenerationRequest): Promise<Recipe> => {
  try {
    const { ingredients, difficulty, language = 'ko' } = request;
    
    // Create the prompt for the AI
    const prompt = createRecipePrompt(ingredients, difficulty, language);
    
    // Call the AI API
    const response = await fetch(AI_API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${AI_API_KEY}`,
      },
      body: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: getSystemPrompt(language),
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        max_tokens: 1000,
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      throw new Error(`AI API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const aiResponse = parseAIResponse(data.choices[0].message.content);
    
    // Create a Recipe object
    const recipe: Recipe = {
      id: Date.now().toString(),
      title: aiResponse.title,
      ingredients: aiResponse.ingredients,
      steps: aiResponse.steps,
      difficulty: aiResponse.difficulty,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };

    return recipe;
  } catch (error) {
    console.error('Error generating recipe:', error);
    throw new Error('Failed to generate recipe. Please try again.');
  }
};

/**
 * Create a prompt for recipe generation
 */
const createRecipePrompt = (ingredients: string[], difficulty: DifficultyLevel, language: string): string => {
  const ingredientsList = ingredients.join(', ');
  
  if (language === 'ko') {
    return `다음 재료들을 사용해서 ${difficulty} 난이도의 요리 레시피를 만들어주세요: ${ingredientsList}

응답은 반드시 다음 JSON 형식으로 해주세요:
{
  "title": "요리 이름",
  "ingredients": ["재료1", "재료2", "재료3"],
  "steps": ["단계1", "단계2", "단계3"],
  "difficulty": "${difficulty}"
}`;
  } else {
    return `Create a ${difficulty} difficulty recipe using these ingredients: ${ingredientsList}

Please respond in the following JSON format:
{
  "title": "Recipe Name",
  "ingredients": ["ingredient1", "ingredient2", "ingredient3"],
  "steps": ["step1", "step2", "step3"],
  "difficulty": "${difficulty}"
}`;
  }
};

/**
 * Get system prompt based on language
 */
const getSystemPrompt = (language: string): string => {
  if (language === 'ko') {
    return `당신은 전문 요리사입니다. 주어진 재료로 맛있고 실용적인 레시피를 만들어주세요. 
레시피는 명확하고 따라하기 쉬워야 하며, 주어진 난이도에 맞아야 합니다.
- Beginner: 간단하고 기본적인 요리법
- Intermediate: 중간 정도의 기술이 필요한 요리법  
- Advanced: 고급 기술이나 특별한 조리법이 필요한 요리법
- Chef: 전문가 수준의 복잡한 요리법

응답은 반드시 유효한 JSON 형식이어야 합니다.`;
  } else {
    return `You are a professional chef. Create delicious and practical recipes using the given ingredients.
The recipe should be clear, easy to follow, and match the specified difficulty level.
- Beginner: Simple and basic cooking methods
- Intermediate: Moderate skill level required
- Advanced: Advanced techniques or special cooking methods required  
- Chef: Professional level complex recipes

Your response must be in valid JSON format.`;
  }
};

/**
 * Parse AI response and extract recipe data
 */
const parseAIResponse = (content: string): AIResponse => {
  try {
    // Try to extract JSON from the response
    const jsonMatch = content.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No JSON found in AI response');
    }
    
    const parsed = JSON.parse(jsonMatch[0]);
    
    // Validate the response structure
    if (!parsed.title || !parsed.ingredients || !parsed.steps || !parsed.difficulty) {
      throw new Error('Invalid recipe structure in AI response');
    }
    
    return {
      title: parsed.title,
      ingredients: Array.isArray(parsed.ingredients) ? parsed.ingredients : [parsed.ingredients],
      steps: Array.isArray(parsed.steps) ? parsed.steps : [parsed.steps],
      difficulty: parsed.difficulty as DifficultyLevel,
    };
  } catch (error) {
    console.error('Error parsing AI response:', error);
    // Return a fallback recipe if parsing fails
    return createFallbackRecipe();
  }
};

/**
 * Create a fallback recipe when AI generation fails
 */
const createFallbackRecipe = (): AIResponse => {
  return {
    title: '간단한 볶음요리',
    ingredients: ['양파', '당근', '감자', '간장', '참기름'],
    steps: [
      '재료를 적당한 크기로 썰어주세요.',
      '팬에 기름을 두르고 양파를 먼저 볶아주세요.',
      '당근과 감자를 넣고 함께 볶아주세요.',
      '간장과 참기름으로 간을 맞춰주세요.',
      '모든 재료가 익으면 완성입니다.',
    ],
    difficulty: 'Beginner' as DifficultyLevel,
  };
};

/**
 * Validate ingredients before sending to AI
 */
export const validateIngredients = (ingredients: string[]): boolean => {
  if (!ingredients || ingredients.length === 0) {
    return false;
  }
  
  // Check if all ingredients are non-empty strings
  return ingredients.every(ingredient => 
    typeof ingredient === 'string' && ingredient.trim().length > 0
  );
};

/**
 * Get estimated cooking time based on difficulty
 */
export const getEstimatedCookingTime = (difficulty: DifficultyLevel): string => {
  switch (difficulty) {
    case 'Beginner':
      return '15-30분';
    case 'Intermediate':
      return '30-60분';
    case 'Advanced':
      return '60-90분';
    case 'Chef':
      return '90분 이상';
    default:
      return '30분';
  }
};
