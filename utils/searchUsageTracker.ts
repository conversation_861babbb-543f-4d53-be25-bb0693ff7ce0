import AsyncStorage from '@react-native-async-storage/async-storage';

// Storage keys
const SEARCH_USAGE_COUNT_KEY = 'search_usage_count';
const REVIEW_PROMPT_SHOWN_KEY = 'review_prompt_shown';

/**
 * Get the current search usage count
 * @returns Promise that resolves to the current search count
 */
export const getSearchUsageCount = async (): Promise<number> => {
  try {
    const count = await AsyncStorage.getItem(SEARCH_USAGE_COUNT_KEY);
    return count ? parseInt(count, 10) : 0;
  } catch (error) {
    console.error('Error getting search usage count:', error);
    return 0;
  }
};

/**
 * Increment the search usage count
 * @returns Promise that resolves to the new search count
 */
export const incrementSearchUsageCount = async (): Promise<number> => {
  try {
    const currentCount = await getSearchUsageCount();
    const newCount = currentCount + 1;
    await AsyncStorage.setItem(SEARCH_USAGE_COUNT_KEY, newCount.toString());
    return newCount;
  } catch (error) {
    console.error('Error incrementing search usage count:', error);
    throw error;
  }
};

/**
 * Check if the review prompt has been shown
 * @returns Promise that resolves to boolean indicating if review prompt was shown
 */
export const hasReviewPromptBeenShown = async (): Promise<boolean> => {
  try {
    const shown = await AsyncStorage.getItem(REVIEW_PROMPT_SHOWN_KEY);
    return shown === 'true';
  } catch (error) {
    console.error('Error checking review prompt status:', error);
    return false;
  }
};

/**
 * Mark the review prompt as shown
 * @returns Promise that resolves when the status is saved
 */
export const markReviewPromptAsShown = async (): Promise<void> => {
  try {
    await AsyncStorage.setItem(REVIEW_PROMPT_SHOWN_KEY, 'true');
  } catch (error) {
    console.error('Error marking review prompt as shown:', error);
    throw error;
  }
};

/**
 * Check if the user should be shown a review prompt
 * @returns Promise that resolves to boolean indicating if review prompt should be shown
 */
export const shouldShowReviewPrompt = async (): Promise<boolean> => {
  try {
    const searchCount = await getSearchUsageCount();
    const reviewPromptShown = await hasReviewPromptBeenShown();
    
    // Show review prompt after 3 searches and only if not shown before
    return searchCount >= 3 && !reviewPromptShown;
  } catch (error) {
    console.error('Error checking if review prompt should be shown:', error);
    return false;
  }
};

/**
 * Reset the search usage count (for testing purposes)
 * @returns Promise that resolves when the count is reset
 */
export const resetSearchUsageCount = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem(SEARCH_USAGE_COUNT_KEY);
  } catch (error) {
    console.error('Error resetting search usage count:', error);
    throw error;
  }
};

/**
 * Reset the review prompt status (for testing purposes)
 * @returns Promise that resolves when the status is reset
 */
export const resetReviewPromptStatus = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem(REVIEW_PROMPT_SHOWN_KEY);
  } catch (error) {
    console.error('Error resetting review prompt status:', error);
    throw error;
  }
};
