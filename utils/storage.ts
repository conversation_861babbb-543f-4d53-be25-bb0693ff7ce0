import { FeedbackLanguage } from '@/types';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface DailyChallengeData {
  questionId: string;
  date: string; // YYYY-MM-DD format
  completed: boolean;
}

// Recipe-related types
export type DifficultyLevel = 'Beginner' | 'Intermediate' | 'Advanced' | 'Chef';

export interface Recipe {
  id: string;
  title: string;
  ingredients: string[];
  steps: string[];
  difficulty: DifficultyLevel;
  imageUri?: string; // Optional image URI
  createdAt: number; // timestamp
  updatedAt: number; // timestamp
}

const ATTEMPTS_STORAGE_KEY = 'topik_writing_attempts';
const FEEDBACK_LANGUAGE_KEY = 'feedback_language';
const UI_LANGUAGE_KEY = 'ui_language';
const QUESTIONS_STORAGE_KEY = 'cached_questions_data';
const DICTIONARY_FEATURE_FLAG_KEY = 'dictionary_feature_enabled';
const TIMED_EXAM_FEATURE_FLAG_KEY = 'timed_exam_feature_enabled';
const TIMED_EXAM_SESSIONS_KEY = 'timed_exam_sessions';
const TIMED_EXAM_ATTEMPTS_KEY = 'timed_exam_attempts';
const DAILY_CHALLENGE_KEY = 'daily_challenge_data';
const WORD_TAP_ONBOARDING_KEY = 'word_tap_onboarding_completed';
const WRITING_TIPS_EXPANDED_KEY = 'writing_tips_expanded';
const DICTIONARY_TRANSLATION_LANGUAGE_KEY = 'dictionary_translation_language';
const RECIPES_STORAGE_KEY = 'saved_recipes';
const ONBOARDING_STATUS_KEY = 'onboarding_status_map';

/**
 * Extracts individual question attempts from a timed exam attempt
 * @param timedAttempt The timed exam attempt to flatten
 * @returns Array of individual question attempts
 */
export const extractIndividualAttemptsFromTimedExam = (timedAttempt: TimedExamAttempt): QuestionAttempt[] => {
  const attempts: QuestionAttempt[] = [];

  for (let index = 0; index < timedAttempt.questionIds.length; index++) {
    const questionId = timedAttempt.questionIds[index];
    const feedback = timedAttempt.feedbacks[index];
    const answer = timedAttempt.answers[questionId] || '';

    // Ensure we have valid feedback for this index
    if (!feedback) {
      console.warn(`Missing feedback for question ${questionId} at index ${index} in timed exam ${timedAttempt.id}`);
      continue;
    }

    // Filter out questions with empty answers
    if (!answer.trim()) {
      continue;
    }

    attempts.push({
      id: `${timedAttempt.id}_q${index}`,
      questionId: questionId as string,
      answer,
      feedback,
      feedbackLanguage: timedAttempt.feedbackLanguage,
      timestamp: timedAttempt.timestamp,
      // Additional metadata to track source
      sourceType: 'timed-exam' as const,
      sourceAttemptId: timedAttempt.id,
      examId: timedAttempt.examId,
    });
  }

  return attempts;
};

/**
 * Gets the question index within a timed exam for a given question ID
 * @param questionId The question ID to find
 * @param timedAttemptId The timed exam attempt ID
 * @returns The index (0-3) or -1 if not found
 */
export const getQuestionIndexInTimedExam = async (questionId: string, timedAttemptId: string): Promise<number> => {
  try {
    const timedAttempt = await getTimedExamAttemptById(timedAttemptId);
    if (!timedAttempt) return -1;
    return timedAttempt.questionIds.indexOf(questionId as any);
  } catch (error) {
    console.error('Error getting question index in timed exam:', error);
    return -1;
  }
};

export const saveAttempt = async (attempt: QuestionAttempt): Promise<void> => {
  try {
    const existingAttemptsJSON = await AsyncStorage.getItem(ATTEMPTS_STORAGE_KEY);
    const existingAttempts: QuestionAttempt[] = existingAttemptsJSON ? JSON.parse(existingAttemptsJSON) : [];

    const updatedAttempts = [...existingAttempts, attempt];

    await AsyncStorage.setItem(ATTEMPTS_STORAGE_KEY, JSON.stringify(updatedAttempts));
  } catch (error) {
    console.error('Error saving attempt:', error);
    throw error;
  }
};

export const getAttempts = async (): Promise<QuestionAttempt[]> => {
  try {
    // Get individual attempts
    const individualAttemptsJSON = await AsyncStorage.getItem(ATTEMPTS_STORAGE_KEY);
    const individualAttempts: QuestionAttempt[] = individualAttemptsJSON ? JSON.parse(individualAttemptsJSON) : [];

    // Mark individual attempts with sourceType if not already set
    const markedIndividualAttempts = individualAttempts.map((attempt) => ({
      ...attempt,
      sourceType: attempt.sourceType || ('individual' as const),
    }));

    // Get timed exam attempts and flatten them
    const timedExamAttempts = await getTimedExamAttempts();
    const flattenedTimedExamAttempts = timedExamAttempts.flatMap(extractIndividualAttemptsFromTimedExam);

    // Combine and sort by timestamp (most recent first)
    const allAttempts = [...markedIndividualAttempts, ...flattenedTimedExamAttempts];
    return allAttempts.sort((a, b) => b.timestamp - a.timestamp);
  } catch (error) {
    console.error('Error getting attempts:', error);
    return [];
  }
};

export const getAttemptById = async (id: string): Promise<QuestionAttempt | null> => {
  try {
    const attempts = await getAttempts();
    const attempt = attempts.find((a) => a.id === id);
    return attempt || null;
  } catch (error) {
    console.error('Error getting attempt by ID:', error);
    return null;
  }
};

export const getAttemptsByQuestionId = async (questionId: string): Promise<QuestionAttempt[]> => {
  try {
    const attempts = await getAttempts();
    return attempts.filter((a) => a.questionId === questionId);
  } catch (error) {
    console.error('Error getting attempts by question ID:', error);
    return [];
  }
};

/**
 * Gets all attempts for a specific question type
 * @param questionType The type of question (e.g., 'Q51_52', 'Q53', 'Q54')
 * @returns Array of attempts for the specified question type
 */
export const getAttemptsByQuestionType = async (questionType: QuestionType): Promise<QuestionAttempt[]> => {
  try {
    // Get all attempts
    const attempts = await getAttempts();

    // Get all questions from storage
    const rawQuestions = await getQuestions();
    if (!rawQuestions) {
      return [];
    }

    // Create a map of question IDs to their types for quick lookup
    const questionTypeMap = new Map<string, QuestionType>();
    rawQuestions.forEach((question) => {
      questionTypeMap.set(question.id, question.type);
    });

    // Filter attempts by question type
    return attempts.filter((attempt) => {
      const type = questionTypeMap.get(attempt.questionId);
      return type === questionType;
    });
  } catch (error) {
    console.error('Error getting attempts by question type:', error);
    return [];
  }
};

export const deleteAttempt = async (id: string): Promise<void> => {
  try {
    const attempts = await getAttempts();
    const updatedAttempts = attempts.filter((a) => a.id !== id);
    await AsyncStorage.setItem(ATTEMPTS_STORAGE_KEY, JSON.stringify(updatedAttempts));
  } catch (error) {
    console.error('Error deleting attempt:', error);
    throw error;
  }
};

/**
 * Gets the maximum score achieved for a specific question
 * @param questionId The ID of the question
 * @param questionMaxScore The maximum possible score for this question type
 * @returns The maximum score achieved, or null if no attempts found
 */
export const getMaxScoreForQuestion = async (questionId: string, questionMaxScore: number): Promise<number | null> => {
  try {
    const attempts = await getAttempts();
    const questionAttempts = attempts.filter((attempt) => attempt.questionId === questionId);

    if (questionAttempts.length === 0) {
      return null;
    }

    let maxScore = 0;
    for (const attempt of questionAttempts) {
      if (attempt.feedback.estimatedScoreRange) {
        const [lowerBound, upperBound] = attempt.feedback.estimatedScoreRange;

        // If the upper bound is the max score for the Q type, use the upper bound
        // If it's not, use the midpoint between upper/lower, rounded up
        const attemptScore = upperBound === questionMaxScore ? upperBound : Math.ceil((lowerBound + upperBound) / 2);

        maxScore = Math.max(maxScore, attemptScore);
      }
    }

    return maxScore;
  } catch (error) {
    console.error('Error getting max score for question:', error);
    return null;
  }
};

export const saveFeedbackLanguage = async (language: FeedbackLanguage): Promise<void> => {
  try {
    await AsyncStorage.setItem(FEEDBACK_LANGUAGE_KEY, language);
  } catch (error) {
    console.error('Error saving feedback language:', error);
    throw error;
  }
};

export const getFeedbackLanguage = async (): Promise<FeedbackLanguage | null> => {
  try {
    const language = await AsyncStorage.getItem(FEEDBACK_LANGUAGE_KEY);
    // Check if the language is one of the supported feedback languages
    if (language && SUPPORTED_LANGUAGES.includes(language as FeedbackLanguage)) {
      return language as FeedbackLanguage;
    }
    return null;
  } catch (error) {
    console.error('Error getting feedback language:', error);
    return null;
  }
};

export const saveUILanguage = async (language: FeedbackLanguage): Promise<void> => {
  try {
    await AsyncStorage.setItem(UI_LANGUAGE_KEY, language);
  } catch (error) {
    console.error('Error saving UI language:', error);
    throw error;
  }
};

export const getUILanguage = async (): Promise<FeedbackLanguage | null> => {
  try {
    const language = await AsyncStorage.getItem(UI_LANGUAGE_KEY);
    // Check if the language is one of the supported UI languages
    if (language && SUPPORTED_LANGUAGES.includes(language as FeedbackLanguage)) {
      return language as FeedbackLanguage;
    }
    return null;
  } catch (error) {
    console.error('Error getting UI language:', error);
    return null;
  }
};

// Questions storage functions

/**
 * Saves question data to AsyncStorage
 * @param questions The raw question data to save
 */
export const saveQuestions = async (questions: QuestionRawData[]): Promise<void> => {
  try {
    await AsyncStorage.setItem(QUESTIONS_STORAGE_KEY, JSON.stringify(questions));
  } catch (error) {
    console.error('Error saving questions to storage:', error);
    throw error;
  }
};

/**
 * Loads question data from AsyncStorage
 * @returns The raw question data or null if not found
 */
export const getQuestions = async (): Promise<QuestionRawData[] | null> => {
  try {
    const cachedData = await AsyncStorage.getItem(QUESTIONS_STORAGE_KEY);
    if (cachedData) {
      return JSON.parse(cachedData) as QuestionRawData[];
    }
    return null;
  } catch (error) {
    console.error('Error loading questions from storage:', error);
    return null;
  }
};

/**
 * Saves the dictionary feature flag state
 * @param enabled Whether the dictionary feature is enabled
 */
export const saveDictionaryFeatureFlag = async (enabled: boolean): Promise<void> => {
  try {
    await AsyncStorage.setItem(DICTIONARY_FEATURE_FLAG_KEY, JSON.stringify(enabled));
  } catch (error) {
    console.error('Error saving dictionary feature flag:', error);
    throw error;
  }
};

/**
 * Saves the timed exam feature flag state
 * @param enabled Whether the timed exam feature is enabled
 */
export const saveTimedExamFeatureFlag = async (enabled: boolean): Promise<void> => {
  try {
    await AsyncStorage.setItem(TIMED_EXAM_FEATURE_FLAG_KEY, JSON.stringify(enabled));
  } catch (error) {
    console.error('Error saving timed exam feature flag:', error);
    throw error;
  }
};

/**
 * Gets the timed exam feature flag state
 * @returns Whether the timed exam feature is enabled, or null if not set
 */
export const getTimedExamFeatureFlag = async (): Promise<boolean | null> => {
  try {
    const enabled = await AsyncStorage.getItem(TIMED_EXAM_FEATURE_FLAG_KEY);
    return enabled ? JSON.parse(enabled) : null;
  } catch (error) {
    console.error('Error getting timed exam feature flag:', error);
    return null;
  }
};

// Timed Exam Session Storage Functions

/**
 * Saves a timed exam session to storage
 * @param session The timed exam session to save
 */
export const saveTimedExamSession = async (session: TimedExamSession): Promise<void> => {
  try {
    const existingSessionsJSON = await AsyncStorage.getItem(TIMED_EXAM_SESSIONS_KEY);
    const existingSessions: TimedExamSession[] = existingSessionsJSON ? JSON.parse(existingSessionsJSON) : [];

    // Remove any existing session with the same ID
    const updatedSessions = existingSessions.filter((s) => s.id !== session.id);
    updatedSessions.push(session);

    await AsyncStorage.setItem(TIMED_EXAM_SESSIONS_KEY, JSON.stringify(updatedSessions));
  } catch (error) {
    console.error('Error saving timed exam session:', error);
    throw error;
  }
};

/**
 * Gets a timed exam session by ID
 * @param sessionId The ID of the session to retrieve
 * @returns The session or null if not found
 */
export const getTimedExamSession = async (sessionId: string): Promise<TimedExamSession | null> => {
  try {
    const sessionsJSON = await AsyncStorage.getItem(TIMED_EXAM_SESSIONS_KEY);
    if (!sessionsJSON) return null;

    const sessions: TimedExamSession[] = JSON.parse(sessionsJSON);
    return sessions.find((s) => s.id === sessionId) || null;
  } catch (error) {
    console.error('Error getting timed exam session:', error);
    return null;
  }
};

/**
 * Gets all active timed exam sessions
 * @returns Array of active sessions
 */
export const getActiveTimedExamSessions = async (): Promise<TimedExamSession[]> => {
  try {
    const sessionsJSON = await AsyncStorage.getItem(TIMED_EXAM_SESSIONS_KEY);
    if (!sessionsJSON) return [];

    const sessions: TimedExamSession[] = JSON.parse(sessionsJSON);
    return sessions.filter((s) => !s.isCompleted);
  } catch (error) {
    console.error('Error getting active timed exam sessions:', error);
    return [];
  }
};

/**
 * Deletes a timed exam session
 * @param sessionId The ID of the session to delete
 */
export const deleteTimedExamSession = async (sessionId: string): Promise<void> => {
  try {
    const sessionsJSON = await AsyncStorage.getItem(TIMED_EXAM_SESSIONS_KEY);
    if (!sessionsJSON) return;

    const sessions: TimedExamSession[] = JSON.parse(sessionsJSON);
    const updatedSessions = sessions.filter((s) => s.id !== sessionId);

    await AsyncStorage.setItem(TIMED_EXAM_SESSIONS_KEY, JSON.stringify(updatedSessions));
  } catch (error) {
    console.error('Error deleting timed exam session:', error);
    throw error;
  }
};

// Timed Exam Attempt Storage Functions

/**
 * Saves a timed exam attempt to storage
 * @param attempt The timed exam attempt to save
 */
export const saveTimedExamAttempt = async (attempt: TimedExamAttempt): Promise<void> => {
  try {
    const existingAttemptsJSON = await AsyncStorage.getItem(TIMED_EXAM_ATTEMPTS_KEY);
    const existingAttempts: TimedExamAttempt[] = existingAttemptsJSON ? JSON.parse(existingAttemptsJSON) : [];

    const updatedAttempts = [...existingAttempts, attempt];

    await AsyncStorage.setItem(TIMED_EXAM_ATTEMPTS_KEY, JSON.stringify(updatedAttempts));
  } catch (error) {
    console.error('Error saving timed exam attempt:', error);
    throw error;
  }
};

/**
 * Gets all timed exam attempts
 * @returns Array of timed exam attempts
 */
export const getTimedExamAttempts = async (): Promise<TimedExamAttempt[]> => {
  try {
    const attemptsJSON = await AsyncStorage.getItem(TIMED_EXAM_ATTEMPTS_KEY);
    return attemptsJSON ? JSON.parse(attemptsJSON) : [];
  } catch (error) {
    console.error('Error getting timed exam attempts:', error);
    return [];
  }
};

/**
 * Gets a timed exam attempt by ID
 * @param attemptId The ID of the attempt to retrieve
 * @returns The attempt or null if not found
 */
export const getTimedExamAttemptById = async (attemptId: string): Promise<TimedExamAttempt | null> => {
  try {
    const attempts = await getTimedExamAttempts();
    return attempts.find((a) => a.id === attemptId) || null;
  } catch (error) {
    console.error('Error getting timed exam attempt by ID:', error);
    return null;
  }
};

/**
 * Gets timed exam attempts by exam ID
 * @param examId The exam ID to filter by
 * @returns Array of attempts for the specified exam
 */
export const getTimedExamAttemptsByExamId = async (examId: string): Promise<TimedExamAttempt[]> => {
  try {
    const attempts = await getTimedExamAttempts();
    return attempts.filter((a) => a.examId === examId);
  } catch (error) {
    console.error('Error getting timed exam attempts by exam ID:', error);
    return [];
  }
};

/**
 * Deletes a timed exam attempt
 * @param attemptId The ID of the attempt to delete
 */
export const deleteTimedExamAttempt = async (attemptId: string): Promise<void> => {
  try {
    const attempts = await getTimedExamAttempts();
    const updatedAttempts = attempts.filter((a) => a.id !== attemptId);
    await AsyncStorage.setItem(TIMED_EXAM_ATTEMPTS_KEY, JSON.stringify(updatedAttempts));
  } catch (error) {
    console.error('Error deleting timed exam attempt:', error);
    throw error;
  }
};

// Daily Challenge Storage Functions

// Word Tap Onboarding Storage Functions

/**
 * Saves the word tap onboarding completion state
 * @param completed Whether the user has completed the word tap onboarding
 */
export const saveWordTapOnboardingCompleted = async (completed: boolean): Promise<void> => {
  try {
    await AsyncStorage.setItem(WORD_TAP_ONBOARDING_KEY, JSON.stringify(completed));
  } catch (error) {
    console.error('Error saving word tap onboarding state:', error);
    throw error;
  }
};

/**
 * Gets the word tap onboarding completion state
 * @returns Whether the user has completed the word tap onboarding
 */
export const getWordTapOnboardingCompleted = async (): Promise<boolean> => {
  try {
    const completed = await AsyncStorage.getItem(WORD_TAP_ONBOARDING_KEY);
    return completed ? JSON.parse(completed) : false;
  } catch (error) {
    console.error('Error getting word tap onboarding state:', error);
    return false;
  }
};

/**
 * Gets today's date in YYYY-MM-DD format using device timezone
 * @returns Today's date string
 */
const getTodayDateString = (): string => {
  const today = new Date();
  return today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0') + '-' + String(today.getDate()).padStart(2, '0');
};

/**
 * Saves daily challenge data to storage
 * @param challengeData The daily challenge data to save
 */
export const saveDailyChallengeData = async (challengeData: DailyChallengeData): Promise<void> => {
  try {
    await AsyncStorage.setItem(DAILY_CHALLENGE_KEY, JSON.stringify(challengeData));
  } catch (error) {
    console.error('Error saving daily challenge data:', error);
    throw error;
  }
};

/**
 * Gets daily challenge data from storage
 * @returns The daily challenge data or null if not found
 */
export const getDailyChallengeData = async (): Promise<DailyChallengeData | null> => {
  try {
    const data = await AsyncStorage.getItem(DAILY_CHALLENGE_KEY);
    return data ? JSON.parse(data) : null;
  } catch (error) {
    console.error('Error getting daily challenge data:', error);
    return null;
  }
};

/**
 * Gets today's daily challenge data, checking if it's still valid for today
 * @returns Today's challenge data or null if not found or expired
 */
export const getTodaysDailyChallengeData = async (): Promise<DailyChallengeData | null> => {
  try {
    const data = await getDailyChallengeData();
    const today = getTodayDateString();

    if (data && data.date === today) {
      return data;
    }

    return null;
  } catch (error) {
    console.error("Error getting today's daily challenge data:", error);
    return null;
  }
};

/**
 * Marks today's daily challenge as completed
 */
export const markDailyChallengeCompleted = async (): Promise<void> => {
  try {
    const data = await getTodaysDailyChallengeData();
    if (data) {
      const updatedData = { ...data, completed: true };
      await saveDailyChallengeData(updatedData);
    }
  } catch (error) {
    console.error('Error marking daily challenge as completed:', error);
    throw error;
  }
};

/**
 * Creates a new daily challenge for today with the given question ID
 * @param questionId The ID of the question for today's challenge
 */
export const createTodaysDailyChallenge = async (questionId: string): Promise<DailyChallengeData> => {
  try {
    const today = getTodayDateString();
    const challengeData: DailyChallengeData = {
      questionId,
      date: today,
      completed: false,
    };

    await saveDailyChallengeData(challengeData);
    return challengeData;
  } catch (error) {
    console.error("Error creating today's daily challenge:", error);
    throw error;
  }
};

// Writing Tips Expanded State Storage Functions

/**
 * Saves the writing tips section expanded state
 * @param expanded Whether the writing tips section is expanded
 */
export const saveWritingTipsExpanded = async (expanded: boolean): Promise<void> => {
  try {
    await AsyncStorage.setItem(WRITING_TIPS_EXPANDED_KEY, JSON.stringify(expanded));
  } catch (error) {
    console.error('Error saving writing tips expanded state:', error);
    throw error;
  }
};

/**
 * Gets the writing tips section expanded state
 * @returns Whether the writing tips section is expanded (defaults to true)
 */
export const getWritingTipsExpanded = async (): Promise<boolean> => {
  try {
    const expanded = await AsyncStorage.getItem(WRITING_TIPS_EXPANDED_KEY);
    return expanded ? JSON.parse(expanded) : true; // Default to open
  } catch (error) {
    console.error('Error getting writing tips expanded state:', error);
    return true; // Default to open on error
  }
};

// Dictionary Translation Language Storage Functions

/**
 * Saves the selected dictionary translation language
 * @param language The language code to save
 */
export const saveDictionaryTranslationLanguage = async (language: FeedbackLanguage): Promise<void> => {
  try {
    await AsyncStorage.setItem(DICTIONARY_TRANSLATION_LANGUAGE_KEY, language);
  } catch (error) {
    console.error('Error saving dictionary translation language:', error);
    throw error;
  }
};

/**
 * Gets the selected dictionary translation language
 * @returns The saved language code or 'en' as default
 */
export const getDictionaryTranslationLanguage = async (): Promise<FeedbackLanguage> => {
  try {
    const language = await AsyncStorage.getItem(DICTIONARY_TRANSLATION_LANGUAGE_KEY);
    // Check if the language is one of the supported languages
    if (language && SUPPORTED_LANGUAGES.includes(language as FeedbackLanguage)) {
      return language as FeedbackLanguage;
    }
    return 'en'; // Default to English
  } catch (error) {
    console.error('Error getting dictionary translation language:', error);
    return 'en'; // Default to English on error
  }
};

// Recipe Storage Functions

/**
 * Saves a recipe to storage
 * @param recipe The recipe to save
 */
export const saveRecipe = async (recipe: Recipe): Promise<void> => {
  try {
    const existingRecipesJSON = await AsyncStorage.getItem(RECIPES_STORAGE_KEY);
    const existingRecipes: Recipe[] = existingRecipesJSON ? JSON.parse(existingRecipesJSON) : [];

    // Remove any existing recipe with the same ID
    const updatedRecipes = existingRecipes.filter((r) => r.id !== recipe.id);
    updatedRecipes.push(recipe);

    await AsyncStorage.setItem(RECIPES_STORAGE_KEY, JSON.stringify(updatedRecipes));
  } catch (error) {
    console.error('Error saving recipe:', error);
    throw error;
  }
};

/**
 * Gets all saved recipes
 * @returns Array of recipes sorted by creation date (most recent first)
 */
export const getRecipes = async (): Promise<Recipe[]> => {
  try {
    const recipesJSON = await AsyncStorage.getItem(RECIPES_STORAGE_KEY);
    const recipes: Recipe[] = recipesJSON ? JSON.parse(recipesJSON) : [];
    return recipes.sort((a, b) => b.createdAt - a.createdAt);
  } catch (error) {
    console.error('Error getting recipes:', error);
    return [];
  }
};

/**
 * Gets a recipe by ID
 * @param recipeId The ID of the recipe to retrieve
 * @returns The recipe or null if not found
 */
export const getRecipeById = async (recipeId: string): Promise<Recipe | null> => {
  try {
    const recipes = await getRecipes();
    return recipes.find((r) => r.id === recipeId) || null;
  } catch (error) {
    console.error('Error getting recipe by ID:', error);
    return null;
  }
};

/**
 * Deletes a recipe
 * @param recipeId The ID of the recipe to delete
 */
export const deleteRecipe = async (recipeId: string): Promise<void> => {
  try {
    const recipes = await getRecipes();
    const updatedRecipes = recipes.filter((r) => r.id !== recipeId);
    await AsyncStorage.setItem(RECIPES_STORAGE_KEY, JSON.stringify(updatedRecipes));
  } catch (error) {
    console.error('Error deleting recipe:', error);
    throw error;
  }
};

/**
 * Updates an existing recipe
 * @param recipeId The ID of the recipe to update
 * @param updates Partial recipe data to update
 */
export const updateRecipe = async (recipeId: string, updates: Partial<Omit<Recipe, 'id' | 'createdAt'>>): Promise<void> => {
  try {
    const recipe = await getRecipeById(recipeId);
    if (!recipe) {
      throw new Error(`Recipe with ID ${recipeId} not found`);
    }

    const updatedRecipe: Recipe = {
      ...recipe,
      ...updates,
      updatedAt: Date.now(),
    };

    await saveRecipe(updatedRecipe);
  } catch (error) {
    console.error('Error updating recipe:', error);
    throw error;
  }
};

// Onboarding Storage Functions

/**
 * Onboarding status map type
 * Maps page/feature identifiers to their completion status
 */
export type OnboardingStatusMap = {
  [pageId: string]: boolean;
};

/**
 * Available onboarding page identifiers
 */
export const ONBOARDING_PAGES = {
  MAIN_SCREEN: 'main_screen',
  RECIPE_LIST: 'recipe_list',
  RECIPE_DETAIL: 'recipe_detail',
  RECIPE_EDIT: 'recipe_edit',
} as const;

export type OnboardingPageId = (typeof ONBOARDING_PAGES)[keyof typeof ONBOARDING_PAGES];

/**
 * Gets the onboarding status map from storage
 * @returns The onboarding status map
 */
export const getOnboardingStatusMap = async (): Promise<OnboardingStatusMap> => {
  try {
    const statusMapJSON = await AsyncStorage.getItem(ONBOARDING_STATUS_KEY);
    return statusMapJSON ? JSON.parse(statusMapJSON) : {};
  } catch (error) {
    console.error('Error getting onboarding status map:', error);
    return {};
  }
};

/**
 * Saves the onboarding status map to storage
 * @param statusMap The onboarding status map to save
 */
export const saveOnboardingStatusMap = async (statusMap: OnboardingStatusMap): Promise<void> => {
  try {
    await AsyncStorage.setItem(ONBOARDING_STATUS_KEY, JSON.stringify(statusMap));
  } catch (error) {
    console.error('Error saving onboarding status map:', error);
    throw error;
  }
};

/**
 * Checks if onboarding has been completed for a specific page
 * @param pageId The page identifier to check
 * @returns Whether onboarding has been completed for the page
 */
export const isOnboardingCompleted = async (pageId: OnboardingPageId): Promise<boolean> => {
  try {
    const statusMap = await getOnboardingStatusMap();
    return statusMap[pageId] === true;
  } catch (error) {
    console.error('Error checking onboarding completion:', error);
    return false;
  }
};

/**
 * Marks onboarding as completed for a specific page
 * @param pageId The page identifier to mark as completed
 */
export const markOnboardingCompleted = async (pageId: OnboardingPageId): Promise<void> => {
  try {
    const statusMap = await getOnboardingStatusMap();
    statusMap[pageId] = true;
    await saveOnboardingStatusMap(statusMap);
  } catch (error) {
    console.error('Error marking onboarding as completed:', error);
    throw error;
  }
};

/**
 * Resets onboarding status for a specific page (useful for testing)
 * @param pageId The page identifier to reset
 */
export const resetOnboardingStatus = async (pageId: OnboardingPageId): Promise<void> => {
  try {
    const statusMap = await getOnboardingStatusMap();
    delete statusMap[pageId];
    await saveOnboardingStatusMap(statusMap);
  } catch (error) {
    console.error('Error resetting onboarding status:', error);
    throw error;
  }
};

/**
 * Resets all onboarding statuses (useful for testing)
 */
export const resetAllOnboardingStatuses = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem(ONBOARDING_STATUS_KEY);
  } catch (error) {
    console.error('Error resetting all onboarding statuses:', error);
    throw error;
  }
};
